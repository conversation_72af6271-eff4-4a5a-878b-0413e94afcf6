# 用户手册知识库切块逻辑
重新修改用户手册知识库构建的切块逻辑：
1.按照markdown的语法，构建层次结构
2.每个子块的层级为：config.py中的MARKDOWN_CHILD_PATTERN参数,默认4个# 四级标题。
3.如果上级标题（3个#）内没有四级标题，则该三级标题作为单独的子块。
4.以此类推，再上一级标题（2个#）内没有3级标题，则该二级标题作为单独的子块。
5.父标题（如3个#）与它的第1个子标题（如4个#）之间存在内容，则该父标题作为单独的子块。
示例如下：
```markdown
# 1.章节
这是测试子块1
## 1.1 子章节
这是测试子块2
## 1.2 子章节
这是测试子块3
# 2.章节
## 2.1 子章节
这是测试子块4
# 3.章节
这是测试子块5
##  3.1 子章节
这是测试子块6
### 3.1.1 子章节
#### 3.1.1.1 子章节
这是测试子块7
#### 3.1.1.2 子章节
这是测试子块8
```
将拆分为如下子块：
块1： 1.章节 测试子块1
块2： 1.1 子章节 测试子块2
块3： 1.2 子章节 测试子块3
块5： 2.1 子章节 测试子块4
块6： 3.章节 测试子块5
块7： 3.1 子章节 测试子块6
块8： 3.1.1.1 子章节 测试子块7
块9： 3.1.1.2 子章节 测试子块8


# cosmic校验功能
1. 增加新的python文件，实现cosmic校验功能，使用大模型检查用户提交的cosmic功能拆解是否符合规范。
2. 用户提交的文件是csv格式，参考output-new.csv，解析后可组织为json的层次结构（可以配置去除不必要的字段），减少提示词使用。
3. 大模型的配置依然使用config.py,增加用于检查cosmic功能的大模型配置项。
4. 整个cosmic的数据一次性提交给大模型检查, 输出大模型的检查结果，对不符合的功能过程或子过程可给出修改建议。
5. 根据要求实现功能，系统提示词使用独立的文件：check_prompt.md

# 新增python文件，实现功能需求文档生成
1. 根据cosmic功能拆解的文档，由大模型辅助生成文字性的功能需求描述和关键时序图。
2. 使用单独的系统提示词文件，用于生成功能需求描述和关键时序图。
3. 生成的功能需求文档为markdown格式，结构如下：
```json
## 2.1 一级功能需求 （对应一级模块）
### 2.1.1 二级功能需求 （对应二级模块）
#### 2.1.1.1 三级功能需求 （对应三级模块）
##### 2.1.1.1.1 功能过程 （对应功能过程）
###### 2.1.1.1.1.1 关键时序图/业务逻辑图
（结合触发事件、子过程、知识库的相关信息，生成可渲染的Mermaid语法图）
###### 2.1.1.1.1.2 需求描述 
（结合子过程、数据组及属性及知识库的相关信息，描述该功能过程）
## 2.2 一级功能需求 （对应一级模块）
....（省略）

```
4. 大模型请求可直接调用main.py中的call_LLM方法，知识库使用knowledge_base.py的get_context_for_module方法
5. 分批次生成功能文档，每个批次可包含多个三级模块(最多50个子过程)。
6. markdown文档的起始序号可配置，比如: 起始=3.1，后面的一级模块依次为：3.2、3.3。

## 优化
1. 增加一个WEB UI的python文件，可调用doc_generator.py生成markdown文档，并可以WEB在线渲染。
2. 前端markdown渲染界面支持"mermaid"时序图的展示
3. 前端界面提供下拉框，可选择本项目目录下的xlsx文件或csv文件，如果对应的md文件已经存在，则直接渲染，否则需要生成markdown文件后再渲染。
4. 已经生成markdown的xlsx文件或csv文件，可以选择重新生成markdown文件。
5. 输出目录结构调整为：
```markdown
## 2.1 一级功能需求 （对应一级模块）
 (该一级模块的功能简介，描述)
### 2.1.1 关键时序图/业务逻辑图
 (顺序列出各三级模块名称及时序图)
（三级模块时序图：可结合功能过程、子过程、触发事件、数据组等相关信息，生成可渲染的Mermaid语法图）
### 2.1.2 功能需求描述
（详细描述该一级模块的功能）
#### 2.1.2.1 二级功能需求 （对应二级模块）
##### 2.1.2.1.1 三级功能需求 （对应三级模块）
{三级模块名称} 包含如下功能：
{顺序列出该三级模块所包含的各功能过程名称}
###### 2.1.2.1.1.1 功能过程 （对应功能过程）
***功能简介***
{功能过程名称}
***功能要求***
{顺序列出各子过程描述列的名称}

## 2.2 一级功能需求 （对应一级模块）
....（省略）

```
6. 根据新的目录结构优化提示词，并约束：只为三级模块生成时序图，功能过程也生成了时序图。
7. 由于是按照三级模块调用大模型生成的时序图，可以从三级模块逐层向上组织文档内容,形成一级模块的功能描述和时序图。

## 改进
1.当前的WEB UI不支持sequenceDiagram时序图，如图，增加这个时序图渲染支持。
2.选择csv/xlsx文件后，如果有markdown文件，可直接在当前页面渲染，不用打开新的标签页。
3.一级模块的功能需求描述是空的，如图2.1.2和2.1.2.1 之间需要有文字性的功能需求描述，可根据各子级模块的名称进行综合描述并补充到这里。

# WEB ui功能支持
1. web_ui.py的render_markdown_with_mermaid方法中，html_template的内容改为.html文件存储，并渲染后台生成的html_content。
2. 左侧菜单栏增加菜单，实现以下功能：
  -2.1 功能拆解：选择xlsx文件和功能拆解提示词文件，调用main.py的make_cosmic方法，生成cosmic功能分解文件。
  -2.2 文档生成：选择xlsx文件和文档生成的提示词文件，调用当前项目的功能生成markdown文件，并使用当前web_ui的功能渲染、查看markdown文件。
  -2.3 提示词工程：使用markdown编辑器，修改当前目录下的提示词文件。支持文件版本管理，修改后保存时自动生成新的版本文件名。 提示词文件存储到当前目录的data目录下，文件名格式为：{功能拆解/文档生成}_提示词.{版本号}.md。默认使用最新版本提示词文件。
  - 2.4 系统配置管理： config.py中的配置改为使用.env文件，并且可以通过WEB界面进行配置修改。

# 多线程处理
main.py增加多线程cosmic拆解功能：
1.config.py增加多线程配置项
2.按照线程数把二级模块分配到线程，每个线程最少1个二级模块，线程数<二级模块数时，线程数=二级模块数。
3.需要处理all_results的并发写入锁控制。
4.最终输出的all_results中，依然按照原来的二级模块顺序输出.

# doc_generator.py多线程功能
1. generate_requirements_document方法增加多线程处理。
2. create_batches方法按照一级模块组织，并以一级模块为单位分配到线程，每个线程最少1个一级模块的所有批次，线程数<一级模块数时，线程数=一级模块数。
2. 需要处理并发锁控制
3. merge_batch_contents合并所有批次时，按照原来一级模块的顺序输出。

#  doc_generator.py优化
1. 修改：只让LLM大模型生成时序图和一级模块的功能详述，其他内容直接根据dataframe组织。
2. 生成的功能需求文档结构参考doc_prompt.md中的输出结构进行如下调整：

```markdown
## {起始序号}.{一级序号} 一级功能需求 （由大模型输出）
{该一级模块的功能简介，描述} 

### {起始序号}.{一级序号}.1 关键时序图/业务逻辑图（由大模型输出）
1.{三级模块名称} - 时序图
2.{三级模块名称} - 时序图 

### {起始序号}.{一级序号}.2 功能需求描述（由大模型输出）
{详细描述该一级模块的功能}

#### {起始序号}.{一级序号}.2.{二级序号} 二级功能需求 （根据dataframe织组）

##### {起始序号}.{一级序号}.2.{二级序号}.{三级序号} 三级功能需求 （根据dataframe织组）
{三级模块名称} 包含如下功能：<br/>
{顺序列出该三级模块所包含的各功能过程名称}，示例如下：
  1.功能过程1<br/>
  2.功能过程2<br/>

###### {起始序号}.{一级序号}.2.{二级序号}.{三级序号}.{功能序号} 功能过程 （根据dataframe织组）
***功能简介*** <br/>
   {功能过程名称}<br/>
***功能要求*** <br/>
{顺序列出各子过程描述列的名称，不含数据组、数据属性、数据移动等内容}，示例如下：
   1.子过程1<br/>
   2.子过程2<br/>
```

2. 实现该功能，优化提示词，可以让LLM输出json格式，方便后续处理。

3. 依然保留原来的多线程批次处理功能
4. 一级模块的功能简介和详细描述和三级模块的时序图分开 独立调用LLM

# cosmic功能拆解校验
增加python文件，实现cosmic校验功能，对main.py生成的cosmic功能拆解excel文件，检查如下问题：
1.不同行的数据属性必须不能相同。
2.不同行的功能过程、子过程不能完全相同。
3.每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E
4.数据属性使必须用中文，可以有中文+英文缩写，如：操作日志ID
5.数据组的内容需要包含三级模块中的主要实体名称，比如：三级模块=证书认证模块注销证书下载，数据组=证书认证模块注销证书下载文件,e而不能只是：注销证书下载文件
检查的方法：
- 可以二级模块为单位，组织为层次的json结构，调用llm_util.py的call_LLM方法，让大模型批量检查和修复，并返回修复结果。支持多线程并行处理。并根据修复结果更新数据集，写入新的文件。使用独立的系统提示词文件，要求简单扼要.

## 改进
## A.把cosmic校验功能分开,以下3个功能使用python程序检查，并输出检查结果：
 - 1.不同行的数据属性必须不能相同。
 - 2.不同行的功能过程、子过程不能完全相同。
 - 3.每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E
 ## B 以下2个功能依然使用LLM检查并修复：
 - 4.数据属性使必须用中文，可以有中文+英文缩写，如：操作日志ID
 - 5.数据组的内容需要包含三级模块中的主要实体名称，比如：三级模块=证书认证模块注销证书下载，数据组=证书认证模块注销证书下载文件,而不能只是：注销证书下载文件

## C 修复后的结果更新到原来的数据集中， 并输出到新文件中。
## D 清除原来的校验逻辑。
## E 优化提示词

## 再次修改
1.把“每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E”的规则也让大模型检查并修复。
2.以三级模块为单位进行检查和修复，需要修改提示词中的输出结构为：
```json
[
    {
      "三级功能模块名称(保持原值)":
        [
            {
                "功能用户": "保持原值",
                "触发事件": "保持原值",
                "功能过程": "保持原值",
                "子过程": [
                    {"子过程描述": "修复后的值或原值", "数据移动类型": "修复后的值或原值", "数据组": "修复后的值或原值", "数据属性": "修复后的值或原值", "CFP": 1,"修复说明": "具体修复了什么问题，如果没有修复则为空"},
                    {"子过程描述": "修复后的值或原值", "数据移动类型": "修复后的值或原值", "数据组": "修复后的值或原值", "数据属性": "修复后的值或原值", "CFP": 1,"修复说明": "具体修复了什么问题，如果没有修复则为空"}
                ]
            }
        ]
    }
]
```

和代码处理。

# 再次修改：
1.使用python检查“每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E”
2.对于不符合规则的功能过程，可以通过添加、修改或重新排序子过程来修复序列问题 。如下修改：
  - 如果第一个子过程数据移动类型不是E，将第一个E的子过程移到开头
  - 如果最后一个子过程数据移动类型不是W或X，将该子过程往前移，直到顺序满足要求。
  - 如果有连续的子过程数据移动类型是E，将后续的E改为R
3.简化提示词，只需要检查并修复："子过程描述"、"数据组"、"数据属性"，输入依然以三级模块组织，包括："三级模块名称","功能过程"，"子过程描述"、"数据组"、"数据属性" 。其他数据保留dataframe中的原来的值。

# 修复
1. run_llm_checks_and_fixes方法在多线程处理二级模块的llm校验结果合并时，按照原来的二级模块顺序输出：可以对二级模块编号，然后按照原来的编号顺序合并（不要输出编号）。
2. 在调用run_llm_checks_and_fixes之后，再增加使用python检查修复数据组属性一致性问题，要求：相同的数据组的属性保持一致，如果不一致，则合并数据组的属性值（不能重复），并统一替换为一致的值。
3. 在调用run_llm_checks_and_fixes之后，再增加使用python检查数据组和属性值是否还有纯英文，如果有则调用llm翻译并修复。

# 提示词优化
优化prompt.md的提示词，去掉一些有冲突的要求，并强调以下cosmic拆解要求，：### 1. 子过程描述的实体名称检查 
- **规则**：子过程描述中需要包含三级模块或功能过程的主要实体名称
- **示例1**：
  - 三级功能模块=签名验签物理密码机管理, 功能过程=签名验签物理密码机新增
  - **正确的子过程描述**：输入签名验签物理密码机新增信息
  - **错误的子过程描述**：输入密码机新增信息（缺少"签名验签物理密码机"实体名称）
- **示例2**：
  - 三级功能模块=签名验签设备主密钥管理，功能过程内容=签名验签设备主密钥配置
  - **正确的子过程描述**：返回签名验签设备主密钥查询结果
  - **错误的子过程描述**：返回主密钥查询结果（缺少"签名验签设备主密钥"实体名称）
- **修复方法**：在子过程描述中三级模块或功能过程的主要实体名称

### 2. 数据组实体名称检查
- **规则**：数据组内容需要包含子过程中的主要实体名称
- **示例1**：
  - 子过程描述：读取签名验签设备类型信息
  - **正确的数据组名称**：签名验签设备类型
  - **错误的数据组名称**：设备类型（缺少"签名验签"实体名称）
- **示例2**：
  - 子过程描述：记录证书认证模块CRL下载日志
  - **正确的数据组名称**：证书认证模块CRL下载日志
  - **错误的数据组名称**：CRL下载日志（缺少"证书认证模块"实体名称）
- **示例3**：
  - 子过程描述：读取PKI服务镜像信息
  - **正确的数据组名称**：PKI服务容器镜像
  - **错误的数据组名称**：容器镜像（缺少"PKI服务"实体名称）
- **修复方法**：根据子过程描述重新提炼数据组名称
### 3. 数据移动类型检查
- **规则**：数据移动类型不能空；每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E。
### 4. 数据组实体名称
- **规则**：数据组实体名称和数据属性名称必须是中文。

# WEB界面调整
## cosmic拆解功能
1. 支持软件功能清单文件(excel)的上传管理和删除，上传的excel文件存放到data/uploads目录，支持在线预览。
2. 对上传的软件功能清单文件进行格式校验，要求：在第2个sheet中，标题为：一级功能模块,二级功能模块,三级功能模块,功能过程,功能描述,预估工作量（人天）
3. 对上传的软件功能清单文件文件调用main.py的do_cosmic_file方法进行cosmic功能拆解，拆解后的文件存放到data/outputs目录，拆解的excel文件增加时间戳后缀，支持在线预览。
4. 一个软件功能清单文件可以支持多次功能拆解调用，拆解的结果文件可以删除、下载。
5. cosmic功能拆解时可以选择data/prompts目录下的不同版本的提示词文件，do_cosmic_file方法增加提示词文件的参数。
## cosmic功能拆解文件的检查和修复
1. 调用cosmic_validator.py的validate_file()方法，选择data/outputs目录下的文件进行数据检查和修复，并输出检查报告。
2. 修复后的文件+"_修复_{事件戳}"后缀，存放到data/outputs目录，支持在线预览、下载和删除，可删除。
3. 修复后文件和功能拆解文件都可以直接与功能文件关联，删除软件功能清单时，可以把相关的功能拆解文件、修复文件一起删除。
## word文档转换功能
1. 可选择data/outputs目录下的功能拆解文件，调用doc_generator.py的generate_requirements_document()方法生成markdown文件，扩展名为.
md，存放到data/words目录，支持在线预览、下载和删除。
2. markdown文件可支持word文件下载，如果选择word文件下载，则先调用step6-generate_mermaid.py的replace_mermaid_with_images方法转为word，再下载。
## 同步config.py中的配置信息到.env中，并支持在线修改，修改各.py文件，使用.env中的配置信息。
## 提示词文件编辑功能只保留"cosmic拆解的提示词"。

## 移除当前UI中与本次修改无关的功能代码。

# 问题
1. 文件上传管理界面,点击选择文件 弹出对话框选择文件后，又再次弹出对话框选文件。
2. 在生成了consmic功能拆解文件后，访问主页index.html渲染错误：
 File "/mnt/d/aitools/cosmic/templates/index.html", line 334, in top-level template code
    <div class="text-center text-muted py-4" id="noRepairFiles" {% if output_files|selectattr('name', 'search', '_修复_')|list %}style="display: none;"{% endif %}>
jinja2.exceptions.TemplateRuntimeError: No test named 'search'.

3. 提示词文件管理：增加另存功能，可以存储为新的提示词文件。
4. 提示词文件管理：编辑区域增加markdown渲染标签
5. 提示词文件管理：只显示"prompt"前缀的md文件，另存的文件也是带"prompt"前缀

# 问题2
 File "/mnt/d/aitools/cosmic/templates/index.html", line 334, in top-level template code
    <div class="text-center text-muted py-4" id="noRepairFiles" {% if output_files|selectattr('name', 'match', '.*_修复_.*')|list %}style="display: none;"{% endif %}>
jinja2.exceptions.TemplateRuntimeError: No test named 'match'.

解决问题，并通过访问主页检查是否修复

# 界面优化
1. 功能拆解、检查修复、文档生成 的过程，增加一个界面上的计时器，显示已经进行的时间。
2. "check_fix_prompt.md"做为系统修复提示词，也在提示词管理显示及编辑修改，不支持另存。
3. 大模型的配置支持多种Provider：
   - opeanai 兼容 、阿里云百炼、硅基流动
   - api key在界面上以****密文态显示
   - 在系统配置中以下拉框选择提供商，并进行配置。并把下拉框选中的Provider做为LLM默认配置。
4. 检查修复功能：修复完成后，输出修复报告的内容。
5. 修改config.py把变量修改为：加载自.env文件。
6. excel文件的在线预览：显示所有行，并支持单元格合并的样式渲染。
7. 每个功能执行 停留在当前页面，不要跳转到主页（文件上传）的页面。
8. markdown的在线预览未渲染mermaid样式的序列交互图形，可使用document_template.html渲染（检查有没有语法错误）

# 改进
1. 根据当前.env和config.py的配置项和代码，修改系统配置界面,补充缺少的配置项及config_manager.py的管理逻辑。
2. 对validator.py的check_duplicate_function_processes方法增加数据组名称、功能过程、子过程描述是否重复的校验(任意一个属性重复都视为重复)，并统一为数据重复问题。
3. 对数据重复问题，可以把有重复属性的子过程行做为一组，提交到大模型进行纠正，方法：
  - 行1的数据组和行2的数据组相同，行3的功能过程与行4的功能过程相同，行4的数据属性与行1的数据属性相同，则行1、2、3、4都可做为一组重复的数据，提交到大模型处理。
  - 输入一级模块、二级模块、三级模块名称、功能过程描述、子过程描述等，让大模型优化功能过程、子过程的数据组和数据属性的名称，确保功能过程、子过程、数据组、数据属性不会存在重复。
  - 让大模型修复问题，并更新到原来的数据行。
  - 该过程可最多重复3次执行，直到没有重复的数据行。
4. validator.py的check_and_fix_data_movement_sequence方法增加数据移动类型空的检查，并调用大模型把这些空的数据移动类型补充上。
5. 增加功能过程过长的检查，一个功能过程不要过长，尽量在10个子过程以内。对于过长的功能过程可以调用大模型进行修改:
  - 把过长的功能过程拆解成多个功能过程，并替换到原来的数据行中。

## 改进2
1. main.py在功能拆解之前（do_cosmic_file方法中），检查一级模块之间、二级模块之间、三级模块之间、功能过程之间是否存在重复，如果有重复给出重复的内容提示并退出，web界面也提示重复内容。
2. web界面的各菜单功能分别拆解到不同的html文件中，每个功能完成后，保持在当前页面不要跳转到主页。
3. 检查修复功能的web界面，增加修复选项功能，可选择的选项包括： ["movement","english","llm","duplicate","long"]及仅检查选项，参考validator.validate_file方法。
4. 修复结束，则web界面显示修复报告，并可以点击查看：修复报告和修复结果。
5. 功能拆解界面，可以增加二级模块和三级模块的下拉框，用于选择只生成其中一部分的功能拆解结果，功能与.env文件中的TEST_LEVEL2_NAME 和TEST_LEVEL3_NAME配置的作用相同，默认是全部模块。