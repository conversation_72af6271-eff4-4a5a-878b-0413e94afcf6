#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验器

对main.py生成的cosmic功能拆解excel文件进行校验
"""

import pandas as pd
import json
import os
import time
import threading
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import defaultdict
from typing import Dict, List, Tuple
import sys
# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
try:
    from csv_2_xls import writie_to_excel
except ImportError:
    # 尝试从src目录导入
    from src.csv_2_xls import writie_to_excel
import config
import llm_util


class CosmicValidator:
    """COSMIC校验器类 - 分离式校验"""

    def __init__(self):
        """初始化校验器"""
        self.llm_prompt = self._load_llm_prompt()
        self.excluded_fields = config.get_OUT_EXCLUDED_FIELDS() or []
        self.batch_count = config.get_BATCH_COUNT()
        self.output_dir = config.get_CHECK_OUTPUT_DIR() or "debug"

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        print(f"COSMIC校验器初始化完成")
        print(f"- 批次大小: {self.batch_count}")
        print(f"- 输出目录: {self.output_dir}")
        print(f"- 排除字段: {self.excluded_fields}")

    def _load_llm_prompt(self) -> str:
        """加载LLM校验提示词"""
        try:
            with open(config.get_CHECK_PROMPT_FILE(), 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"加载提示词失败: {e}!!!")
            raise e
    
    # ==================== Python程序检查方法 ====================

    def check_duplicate_data_attributes(self, df: pd.DataFrame) -> List[Dict]:
        """检查1: 不同行的数据属性必须不能相同"""
        issues = []
        data_attr_col = '数据属性'

        if data_attr_col not in df.columns:
            return issues

        # 统计数据属性出现次数
        attr_counts = df[data_attr_col].value_counts()
        duplicates = attr_counts[attr_counts > 1]

        for attr, count in duplicates.items():
            if pd.notna(attr) and str(attr).strip():
                duplicate_rows = df[df[data_attr_col] == attr].index.tolist()
                issues.append({
                    "issue_type": "数据属性重复",
                    "description": f"数据属性 '{attr}' 在 {count} 行中重复出现",
                    "affected_rows": duplicate_rows,
                    "severity": "高",
                    "value": attr
                })

        return issues

    def check_duplicate_function_processes(self, df: pd.DataFrame) -> List[Dict]:
        """检查2: 不同行的功能过程、子过程、数据组名称不能完全相同"""
        issues = []
        func_process_col = '功能过程'

        # if func_process_col not in df.columns or subprocess_col not in df.columns:
        #     return issues

        # 检查功能过程重复
        if func_process_col in df.columns:
            func_counts = df[func_process_col].value_counts()
            func_duplicates = func_counts[func_counts > 1]
            
            for func_process, count in func_duplicates.items():
                if pd.notna(func_process) and str(func_process).strip():
                    duplicate_rows = df[df[func_process_col] == func_process].index.tolist()
                    issues.append({
                        "issue_type": "数据重复问题",
                        "description": f"功能过程 '{func_process}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "duplicate_type": "功能过程",
                        "value": func_process
                    })
        return issues
    def check_duplicate_data_processes(self, df: pd.DataFrame) -> List[Dict]:
        """检查2: 不同行的子过程、数据组名称、数据属性不能完全相同"""
        issues = []
        func_process_col = '功能过程'
        subprocess_col = '子过程描述'
        data_group_col = '数据组'
        data_attribute_col = '数据属性'

        # if func_process_col not in df.columns or subprocess_col not in df.columns:
        #     return issues

        # 检查子过程描述重复
        if subprocess_col in df.columns:
            subprocess_counts = df[subprocess_col].value_counts()
            subprocess_duplicates = subprocess_counts[subprocess_counts > 1]

            for subprocess, count in subprocess_duplicates.items():
                if pd.notna(subprocess) and str(subprocess).strip():
                    duplicate_rows = df[df[subprocess_col] == subprocess].index.tolist()
                    issues.append({
                        "issue_type": "数据重复问题",
                        "description": f"子过程描述 '{subprocess}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "duplicate_type": "子过程描述",
                        "value": subprocess
                    })

        # 检查数据组名称重复
        if data_group_col in df.columns:
            data_group_counts = df[data_group_col].value_counts()
            data_group_duplicates = data_group_counts[data_group_counts > 1]

            for data_group, count in data_group_duplicates.items():
                if pd.notna(data_group) and str(data_group).strip():
                    duplicate_rows = df[df[data_group_col] == data_group].index.tolist()
                    issues.append({
                        "issue_type": "数据重复问题",
                        "description": f"数据组名称 '{data_group}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "duplicate_type": "数据组名称",
                        "value": data_group
                    })

        # 检查数据属性重复
        if data_attribute_col in df.columns:
            data_attribute_counts = df[data_attribute_col].value_counts()
            data_attribute_duplicates = data_attribute_counts[data_attribute_counts > 1]

            for data_attribute, count in data_attribute_duplicates.items():
                if pd.notna(data_attribute) and str(data_attribute).strip():
                    duplicate_rows = df[df[data_attribute_col] == data_attribute].index.tolist()
                    issues.append({
                        "issue_type": "数据重复问题",
                        "description": f"数据属性 '{data_attribute}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "duplicate_type": "数据属性",
                        "value": data_attribute
                    })

        # 检查功能过程+子过程的组合重复
        df_temp = df.copy()
        df_temp['process_key'] = df_temp[func_process_col].astype(str) + "||" + df_temp[subprocess_col].astype(str)

        process_counts = df_temp['process_key'].value_counts()
        duplicates = process_counts[process_counts > 1]

        for process_key, count in duplicates.items():
            if "||" in process_key:
                func_process, subprocess = process_key.split("||", 1)
                if func_process.strip() and subprocess.strip():
                    duplicate_rows = df_temp[df_temp['process_key'] == process_key].index.tolist()
                    issues.append({
                        "issue_type": "数据重复问题",
                        "description": f"功能过程 '{func_process}' + 子过程 '{subprocess}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "duplicate_type": "功能过程+子过程组合",
                        "value": f"{func_process} + {subprocess}"
                    })

        return issues    

    def check_and_fix_data_movement_sequence(self, df: pd.DataFrame, check_fix = True) -> Tuple[pd.DataFrame, List[Dict]]:
        """检查并修复数据移动序列问题 - 通过移动整行数据"""
        issues = []
        func_process_col = '功能过程'
        movement_col = '数据移动类型'

        if func_process_col not in df.columns or movement_col not in df.columns:
            return df, issues

        # 创建DataFrame副本用于修复
        fixed_df = df.copy()

        # 检查数据移动类型为空的情况
        empty_movement_rows = fixed_df[
            (fixed_df[movement_col].isna()) | 
            (fixed_df[movement_col].astype(str).str.strip() == '')
        ]
        
        if not empty_movement_rows.empty and check_fix:
            # 调用大模型补充空的数据移动类型
            fixed_df, movement_issues = self._fix_empty_data_movement_types(fixed_df, empty_movement_rows)
            issues.extend(movement_issues)

        #return fixed_df, movement_issues
        # 按功能过程分组
        grouped = fixed_df.groupby(func_process_col, sort=False)

        # 存储所有需要重新排列的行
        rows_to_reorder = []
        other_rows = []

        for func_process, group in grouped:
            if pd.isna(func_process) or not str(func_process).strip():
                other_rows.extend(group.index.tolist())
                continue

            group_indices = group.index.tolist()
            movements = group[movement_col].fillna('').astype(str).str.strip().tolist()

            if not movements or all(not m for m in movements):
                other_rows.extend(group_indices)
                continue

            original_movements = movements.copy()
            reordered_indices = self._fix_movement_sequence_by_reordering(group_indices, movements, fixed_df)

            if reordered_indices != group_indices:
                # 记录修复问题
                issue = {
                    "issue_type": "数据移动序列修复",
                    "description": f"功能过程 '{func_process}' 的数据移动序列已通过重新排序修复",
                    "affected_rows": group_indices,
                    "severity": "中",
                    "function_process": func_process,
                    "original_sequence": " -> ".join(original_movements)
                }
                issues.append(issue)

            rows_to_reorder.extend(reordered_indices)

        # 重新构建DataFrame，保持修复后的行顺序
        if rows_to_reorder:
            all_ordered_indices = rows_to_reorder + other_rows
            fixed_df = fixed_df.loc[all_ordered_indices].reset_index(drop=True)

        return fixed_df, issues

    def _fix_empty_data_movement_types(self, df: pd.DataFrame, empty_rows: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """使用大模型补充空的数据移动类型"""
        issues = []
        fixed_df = df.copy()
        
        try:
            # 准备输入数据
            input_data = []
            for idx, row in empty_rows.iterrows():
                input_data.append({
                    'row_index': idx,
                    'level1': row.get('一级功能模块', ''),
                    'level2': row.get('二级功能模块', ''),
                    'level3': row.get('三级功能模块', ''),
                    'function_process': row.get('功能过程', ''),
                    'subprocess': row.get('子过程描述', ''),
                    'data_group': row.get('数据组', ''),
                    'data_attribute': row.get('数据属性', '')
                })
            
            if not input_data:
                return fixed_df, issues
            
            # 调用大模型
            prompt = f"""
请根据以下COSMIC功能拆解数据，为缺失的数据移动类型字段补充合适的值。

数据移动类型应该是以下之一：
- E (Entry): 数据输入
- X (Exit): 数据输出  
- R (Read): 数据读取
- W (Write): 数据写入

输入数据：
{json.dumps(input_data, ensure_ascii=False, indent=2)}

请返回JSON格式的结果，格式如下：
{{
    "results": [
        {{
            "row_index": 行索引,
            "data_movement_type": "数据移动类型",
            "reason": "选择该类型的理由"
        }}
    ]
}}

请确保每个行索引都有对应的数据移动类型。
"""
            
            response = llm_util.call_LLM(prompt, user_input=" ", temperature=0.1)
            result = llm_util.extract_json_from_content(response)
            if not result:
                return fixed_df, issues
            
            # 解析响应
            result = result.get("results",[])
            for item in result:
                 row_index = item.get('row_index')
                 data_movement_type = item.get('data_movement_type')
                 reason = item.get('reason', '')
                 
                 if row_index is not None and data_movement_type:
                     fixed_df.loc[row_index, '数据移动类型'] = data_movement_type
                     
                     issues.append({
                         "issue_type": "数据移动类型补充",
                         "description": f"为第{row_index}行补充数据移动类型: {data_movement_type}",
                         "affected_rows": [row_index],
                         "severity": "中",
                         "reason": reason
                     })
        except Exception as e:
            print(f"数据移动类型补充失败：{e}")
        
        return fixed_df, issues

    def check_long_function_processes(self, df: pd.DataFrame) -> List[Dict]:
        """检查功能过程是否过长（超过10个子过程）"""
        issues = []
        func_process_col = '功能过程'
        max_subprocesses = 10

        if func_process_col not in df.columns:
            return issues

        # 按功能过程分组统计子过程数量
        func_process_counts = df[func_process_col].value_counts()
        long_processes = func_process_counts[func_process_counts > max_subprocesses]

        for func_process, count in long_processes.items():
            if pd.notna(func_process) and str(func_process).strip():
                affected_rows = df[df[func_process_col] == func_process].index.tolist()
                issues.append({
                    "issue_type": "功能过程过长",
                    "description": f"功能过程 '{func_process}' 包含 {count} 个子过程，超过建议的 {max_subprocesses} 个",
                    "affected_rows": affected_rows,
                    "severity": "中",
                    "function_process": func_process,
                    "subprocess_count": count,
                    "max_recommended": max_subprocesses
                })

        return issues

    def fix_long_function_processes(self, df: pd.DataFrame, check_fix=True) -> Tuple[pd.DataFrame, List[Dict]]:
        """修复过长的功能过程，将其拆解成多个功能过程"""
        issues = []
        fixed_df = df.copy()
        
        # 获取过长的功能过程
        long_process_issues = self.check_long_function_processes(fixed_df)
        if not check_fix:
            return df, long_process_issues
        
        for issue in long_process_issues:
            func_process = issue['function_process']
            affected_rows = issue['affected_rows']
            
            try:
                # 获取该功能过程的所有数据
                process_data = fixed_df.loc[affected_rows].copy()
                print(f"  正在修复过长的功能过程：{func_process}")
                
                # 调用大模型进行拆解
                fixed_df, split_issues = self._split_long_function_process(fixed_df, process_data, func_process)
                issues.extend(split_issues)
                
            except Exception as e:
                print(f"拆解功能过程 '{func_process}' 时出错: {e}")
        
        return fixed_df, issues

    def _split_long_function_process(self, df: pd.DataFrame, process_data: pd.DataFrame, original_func_process: str) -> Tuple[pd.DataFrame, List[Dict]]:
        """使用大模型拆解过长的功能过程"""
        issues = []
        fixed_df = df.copy()

        try:
            # 准备输入数据 - 创建索引映射
            input_data = []
            index_mapping = {}  # 原始索引到新索引的映射

            for new_idx, (original_idx, row) in enumerate(process_data.iterrows()):
                index_mapping[new_idx] = original_idx  # 记录映射关系
                input_data.append({
                    'row_index': new_idx,  # 使用新的连续索引
                    'original_index': original_idx,  # 保存原始索引
                    'level1': row.get('一级功能模块', ''),
                    'level2': row.get('二级功能模块', ''),
                    'level3': row.get('三级功能模块', ''),
                    'function_process': row.get('功能过程', ''),
                    'subprocess': row.get('子过程描述', ''),
                    'data_group': row.get('数据组', ''),
                    'data_attribute': row.get('数据属性', ''),
                    'data_movement': row.get('数据移动类型', '')
                })

            # 调用大模型
            prompt = f"""
请将以下过长的COSMIC功能过程拆解成多个功能过程，使得每个功能过程应该包含不超过10个子过程，不少于2个子过程。

原始功能过程：{original_func_process}
子过程数量：{len(input_data)}

输入数据：
{json.dumps(input_data, ensure_ascii=False, indent=2)}

请返回JSON格式的结果，格式如下：
{{
    "split_results": [
        {{
            "new_function_process": "新的功能过程名称",
            "subprocesses": [
                {{
                    "row_index": 行索引,
                    "subprocess": "子过程描述",
                    "data_group": "数据组",
                    "data_attribute": "数据属性",
                    "data_movement": "数据移动类型"
                }}
            ]
        }}
    ]
}}

要求：
1. 每个新功能过程应该包含相关的子过程
2. 每个功能过程最少包含2个子过程，不得超过10个子过程
3. 新功能过程名称应该清晰描述其功能
4. 根据调整后的功能优化数据组、数据属性、数据移动类型等信息
5. 确保所有子过程都被分配到新的功能过程中
"""

            response = llm_util.call_LLM(prompt, user_input=" ", temperature=0.3)
            result = llm_util.extract_json_from_content(response)

            # 解析响应
            if 'split_results' in result:
                # 获取原始行的位置信息
                affected_indices = process_data.index.tolist()
                affected_indices.sort()  # 确保按顺序处理

                # 找到第一个要替换的行的位置
                first_position = affected_indices[0]

                # 准备新的拆解后的数据行
                new_rows = []
                for split_result in result['split_results']:
                    new_func_process = split_result.get('new_function_process', '')
                    subprocesses = split_result.get('subprocesses', [])

                    for subprocess_data in subprocesses:
                        row_index = subprocess_data.get('row_index')
                        if row_index is not None and row_index in index_mapping:
                            # 通过索引映射获取原始索引
                            original_index = index_mapping[row_index]

                            # 获取原始行数据
                            try:
                                original_row = process_data.loc[original_index]
                            except KeyError:
                                # 如果索引不存在，尝试使用iloc
                                if row_index < len(process_data):
                                    original_row = process_data.iloc[row_index]
                                else:
                                    print(f"警告: 无法找到索引 {row_index} 对应的行数据")
                                    continue

                            # 创建新行
                            new_row = original_row.copy()
                            new_row['功能过程'] = new_func_process
                            new_row['子过程描述'] = subprocess_data.get('subprocess', original_row.get('子过程描述', ''))
                            new_row['数据组'] = subprocess_data.get('data_group', original_row.get('数据组', ''))
                            new_row['数据属性'] = subprocess_data.get('data_attribute', original_row.get('数据属性', ''))
                            new_row['数据移动类型'] = subprocess_data.get('data_movement', original_row.get('数据移动类型', ''))

                            new_rows.append(new_row)

                if new_rows:
                    # 将DataFrame转换为列表以便操作
                    df_list = fixed_df.to_dict('records')

                    # 从后往前删除原始行，避免索引变化影响
                    for idx in reversed(affected_indices):
                        if idx < len(df_list):
                            del df_list[idx]

                    # 在原位置插入新行
                    insert_position = first_position
                    for new_row in new_rows:
                        df_list.insert(insert_position, new_row.to_dict())
                        insert_position += 1

                    # 重新创建DataFrame
                    fixed_df = pd.DataFrame(df_list)
                    fixed_df.reset_index(drop=True, inplace=True)

                    issues.append({
                        "issue_type": "功能过程拆解",
                        "description": f"将功能过程 '{original_func_process}' 拆解成 {len(result['split_results'])} 个新功能过程",
                        "affected_rows": affected_indices,
                        "severity": "中",
                        "original_process": original_func_process,
                        "new_processes": [r.get('new_function_process', '') for r in result['split_results']]
                    })

        except Exception as e:
            print(f"拆解功能过程时出错: {e}")

        return fixed_df, issues

    def _fix_movement_sequence_by_reordering(self, indices: List[int], movements: List[str], df: pd.DataFrame) -> List[int]:
        """通过重新排序整行数据来修复数据移动序列"""
        if not movements or not indices:
            return indices

        # 创建索引和移动类型的映射
        index_movement_pairs = list(zip(indices, movements))
        reordered_pairs = index_movement_pairs.copy()

        # 1. 如果第一个不是E，找到第一个E并移到开头
        if movements[0] != 'E':
            e_pair_index = -1
            for i, (idx, movement) in enumerate(reordered_pairs):
                if movement == 'E':
                    e_pair_index = i
                    break

            if e_pair_index > 0:
                # 将包含E的整行移到开头
                e_pair = reordered_pairs.pop(e_pair_index)
                reordered_pairs.insert(0, e_pair)

        # 2. 处理连续的E，将后续的E改为R（修改DataFrame中的值）
        for i in range(1, len(reordered_pairs)):
            if reordered_pairs[i][1] == 'E' and reordered_pairs[i-1][1] == 'E':
                # 修改DataFrame中的数据移动类型
                idx = reordered_pairs[i][0]
                df.loc[idx, '数据移动类型'] = 'R'
                # 更新pair中的移动类型
                reordered_pairs[i] = (idx, 'R')

        # 3. 如果最后一个是R，找到W并将R移到W之前
        current_movements = [pair[1] for pair in reordered_pairs]
        if len(current_movements) > 1 and current_movements[-1] == 'R':
            w_pair_index = -1
            for i in range(len(reordered_pairs) - 1, -1, -1):
                if reordered_pairs[i][1] == 'W':
                    w_pair_index = i
                    break

            if w_pair_index >= 0 and w_pair_index < len(reordered_pairs) - 1:
                # 将包含R的整行移到W之前
                r_pair = reordered_pairs.pop()
                reordered_pairs.insert(w_pair_index, r_pair)

        # 4. 如果过程中有X并且不是最后一个，则X移到最后
        # current_movements = [pair[1] for pair in reordered_pairs]
        # if len(current_movements) > 1 and current_movements[-1] != 'X':
        #     x_pair_index = -1
        #     for i in range(len(reordered_pairs) - 1, -1, -1):
        #         if reordered_pairs[i][1] == 'X':
        #             x_pair_index = i
        #             break

        #     if x_pair_index >= 0 and x_pair_index < len(reordered_pairs) - 1:
        #         # 将包含X的整行移到最后
        #         x_pair = reordered_pairs.pop(x_pair_index)
        #         reordered_pairs.append(x_pair)

        # 返回重新排序后的索引列表
        return [pair[0] for pair in reordered_pairs]    

    def parse_excel_file(self, file_path: str) -> pd.DataFrame:
        """解析Excel或CSV文件"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path)            
            
            # 向后填充一级/二级模块列
            level_1_name, level_2_name, level_3_name, user,event,process_name = "一级功能模块", "二级功能模块","三级功能模块","功能用户","触发事件","功能过程"
            df[[level_1_name, level_2_name, level_3_name, user,event,process_name]] = df[[level_1_name, level_2_name, level_3_name, user,event,process_name]].ffill()
            
            print(f"成功解析文件， 数据行数: {len(df)} , 列数: {len(df.columns)}")
            
            return df
        except Exception as e:
            print(f"解析文件失败: {e}")
            return None
    
    def group_by_level2_module(self, df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """按二级模块分组数据"""
        grouped_data = defaultdict(list)
        
        for _, row in df.iterrows():
            # 跳过空行
            if pd.isna(row.get('三级功能模块')) or str(row.get('三级功能模块')).strip() == '':
                continue
            
            level2_module = str(row.get('二级功能模块', ''))
            
            # 转换为字典，排除指定字段
            row_dict = {}
            for col, value in row.items():
                if col not in self.excluded_fields:
                    row_dict[col] = value if not pd.isna(value) else ""
            
            grouped_data[level2_module].append(row_dict)
        
        print(f"按二级模块分组完成，共 {len(grouped_data)} 个模块")
        for module, data in grouped_data.items():
            print(f"  {module}: {len(data)} 条记录")
        
        return dict(grouped_data)

    def run_llm_checks_and_fixes(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """运行LLM检查和修复"""
        print("开始LLM检查和修复...")

        # 按二级模块分组进行LLM处理
        grouped_data = self.group_by_level2_module(df)
        if not grouped_data:
            print("没有有效数据进行LLM处理")
            return df, []

        # 保存原始二级模块顺序
        original_module_order = list(grouped_data.keys())
        print(f"原始二级模块顺序: {original_module_order}")

        # 确定线程数
        thread_count = self.get_optimal_thread_count(len(grouped_data))
        print(f"使用 {thread_count} 个线程进行LLM处理")

        # 多线程LLM处理
        start_time = time.time()
        all_results = {}

        with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CosmicLLM") as executor:
            # 提交任务
            future_to_module = {}
            for module_name, module_data in grouped_data.items():
                future = executor.submit(self.process_module_with_llm, module_name, module_data)
                future_to_module[future] = module_name

            # 收集结果
            for future in as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    result_module_name, result = future.result()
                    all_results[result_module_name] = result
                except Exception as exc:
                    print(f"模块 {module_name} LLM处理失败: {exc}")
                    all_results[module_name] = {"error": str(exc), "original_data": grouped_data[module_name]}

        processing_time = time.time() - start_time
        print(f"LLM处理完成，耗时: {processing_time:.2f}秒")

        # 按原始顺序合并修复后的数据
        fixed_df, llm_issues = self._merge_llm_results_ordered(all_results, df, original_module_order)

        return fixed_df, llm_issues

    def process_module_with_llm(self, level2_name: str, module_data: List[Dict]) -> Tuple[str, Dict]:
        """使用LLM处理单个二级模块，以三级模块为单位组织数据"""
        # 按三级模块分组数据
        level3_groups = self._group_by_level3_module(module_data)
        print(f"[线程{threading.current_thread().name}] 开始处理二级模块-[{level2_name}], 包含 {len(level3_groups)} 个三级模块")

        # 处理每个三级模块组
        all_fixed_data = []
        all_issues = []

        for level3_module, level3_data in level3_groups.items():
            if config.get_TEST_LEVEL3_NAME() != "" and level3_module != config.get_TEST_LEVEL3_NAME():
                continue
            print(f"[线程{threading.current_thread().name}] 处理三级模块-[{level3_module}], {len(level3_data)} 个子过程")

            # 将三级模块数据组织为与提示词输出相同的结构
            structured_input = self._organize_level3_data_for_llm(level3_module, level3_data)

            # 调用LLM处理
            batch_id = f"{level2_name}_{level3_module}_llm"
            
            fixed_data = level3_data
            for i in range(1):
                #print(f"LLM输入：{structured_input}")
                result = self._call_llm_for_level3_validation(structured_input, batch_id)
                #print(f"LLM输出：{result}")

                issues = []
                if "error" in result:
                    print(f"三级模块 {level3_module} LLM处理失败: {result['error']}")
                    # 如果LLM处理失败，保留原始数据
                    all_fixed_data.extend(fixed_data)
                else:
                    # 处理LLM返回的三级模块结构结果                
                    fixed_data, issues = self._process_level3_llm_result(result, fixed_data)
                
                print(f"三级模块 {level3_module}-[{i}] LLM处理完成: 处理 {len(fixed_data)} 条记录，发现 {len(issues)} 个问题")
                if len(issues) == 0:
                        break

            all_fixed_data.extend(fixed_data)
            all_issues.extend(issues)

        # 汇总结果
        module_result = {
            "module_name": level2_name,
            "original_count": len(module_data),
            "fixed_count": len(all_fixed_data),
            "fixed_data": all_fixed_data,
            "issues_found": all_issues
        }

        print(f"[线程{threading.current_thread().name}] 完成二级功能模块【{level2_name}】：: 原始{len(module_data)}条，修复{len(all_fixed_data)}条")

        return level2_name, module_result

    def _group_by_level3_module(self, module_data: List[Dict]) -> Dict[str, List[Dict]]:
        """按三级模块分组数据"""
        level3_groups = defaultdict(list)

        for record in module_data:
            level3_module = record.get('三级功能模块', '')
            if level3_module:
                level3_groups[level3_module].append(record)

        return dict(level3_groups)

    def _organize_level3_data_for_llm(self, level3_module: str, level3_data: List[Dict]) -> List[Dict]:
        """将三级模块数据组织为与提示词输出相同的结构"""
        # 按功能过程分组
        process_groups = defaultdict(list)

        for record in level3_data:
            function_process = record.get('功能过程', '')
            process_groups[function_process].append(record)

        # 构建结构化数据
        processes = []
        index = 1
        for function_process, records in process_groups.items():
            # 获取第一个记录的基本信息
            first_record = records[0]

            # 构建子过程列表
            subprocesses = []
            for record in records:
                subprocess_info = {
                    "编号": index,
                    "子过程描述": record.get('子过程描述', ''),
                    "数据移动类型": record.get('数据移动类型', ''),
                    "数据组": record.get('数据组', ''),
                    "数据属性": record.get('数据属性', '')
                }
                index += 1
                subprocesses.append(subprocess_info)

            # 构建功能过程信息
            process_info = {
                "功能用户": first_record.get('功能用户', ''),
                "触发事件": first_record.get('触发事件', ''),
                "功能过程": function_process,
                "子过程": subprocesses
            }
            processes.append(process_info)

        # 构建最终结构
        structured_data = [{level3_module: processes}]
        return structured_data

    def _call_llm_for_level3_validation(self, structured_input: List[Dict], batch_id: str) -> Dict:
        """调用LLM进行三级模块校验"""
        # 构建用户输入
        user_input = f"请检查并修复COSMIC数据中的问题。\n\n"
        user_input += "数据（已按三级模块组织）：\n"
        user_input += json.dumps(structured_input, ensure_ascii=False, indent=2)

        try:
            # 调用LLM
            result = llm_util.call_LLM(self.llm_prompt, user_input + " ")

            if result:
                # 解析JSON结果
                parsed_result = llm_util.extract_json_from_content(result)
                if parsed_result:
                    return parsed_result
                else:
                    print(f"{batch_id} JSON解析失败")
                    return {"error": "JSON解析失败", "raw_content": result}
            else:
                print(f"{batch_id} LLM调用失败")
                return {"error": "LLM调用失败"}

        except Exception as e:
            print(f"{batch_id} 处理异常: {e}")
            return {"error": str(e)}

    def _process_level3_llm_result(self, llm_result: List[Dict], original_data: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """处理LLM返回的三级模块结构结果"""
        fixed_data = []
        issues_found = []

        index = 1
        for org_subprocess in original_data:
            # 构建扁平记录
            record = {}
            record["一级功能模块"] = org_subprocess["一级功能模块"]
            record["二级功能模块"] = org_subprocess["二级功能模块"]
            record["三级功能模块"] = org_subprocess["三级功能模块"]
            record["功能用户"] = org_subprocess["功能用户"]
            record["触发事件"] = org_subprocess["触发事件"]
            record["功能过程"] = org_subprocess["功能过程"]
            
            # 从subprocesses中查询index属性相同的子过程
            llm_subprocess = [s for s in llm_result if s.get("编号", 0) == f"{index}"]
            if llm_subprocess:
                subprocess = llm_subprocess[0]
            else:
                subprocess = org_subprocess
            index += 1
            
            # 更新字段值
            record["子过程描述"] = subprocess.get('子过程描述', '')
            record["数据移动类型"] = subprocess.get('数据移动类型', '')
            record["数据组"] = subprocess.get('数据组', '')
            record["数据属性"] = subprocess.get('数据属性', '')
            record["CFP"] = 1
            fixed_data.append(record)

            if llm_subprocess:
                # 收集修复说明
                issue = {
                        "issue_type": "LLM修复",
                        "description": "",
                        "fix_applied": "",
                        "subprocess": record["子过程描述"]
                }
                issues_found.append(issue)
        
        return fixed_data, issues_found    

    def _merge_llm_results_ordered(self, all_results: Dict, original_df: pd.DataFrame, module_order: List[str]) -> Tuple[pd.DataFrame, List[Dict]]:
        """按照原始二级模块顺序合并LLM处理结果"""
        all_fixed_data = []
        all_issues = []

        # 按照原始顺序处理模块
        for module_name in module_order:
            if module_name not in all_results:
                print(f"警告: 模块 {module_name} 没有处理结果")
                continue

            result = all_results[module_name]
            if "error" in result:
                print(f"模块 {module_name} 有错误，使用原始数据: {result['error']}")
                # 使用原始数据
                if "original_data" in result:
                    all_fixed_data.extend(result["original_data"])
                continue

            all_fixed_data.extend(result.get('fixed_data', []))
            all_issues.extend(result.get('issues_found', []))

        # 如果没有修复数据，返回原始数据
        if not all_fixed_data:
            print("没有LLM修复数据，返回原始数据")
            return original_df, all_issues

        # 创建修复后的DataFrame
        try:
            fixed_df = pd.DataFrame(all_fixed_data)
            print(f"LLM修复完成（按原始顺序）: 原始 {len(original_df)} 条 -> 修复后 {len(fixed_df)} 条")
            return fixed_df, all_issues
        except Exception as e:
            print(f"创建修复后DataFrame失败: {e}，返回原始数据")
            return original_df, all_issues
    
    def get_optimal_thread_count(self, module_count: int) -> int:
        """获取最优线程数"""
        if config.get_THREAD_COUNT() > 0:
            optimal_threads = min(config.get_THREAD_COUNT(), module_count)
        else:
            cpu_count = os.cpu_count() or 4
            optimal_threads = min(cpu_count, module_count)
        
        return max(1, optimal_threads)

    def fix_english_content(self, df: pd.DataFrame, check_fix = True) -> Tuple[pd.DataFrame, List[Dict]]:
        """检查并翻译英文内容"""
        print("开始检查并翻译英文内容...")

        issues = []
        data_group_col = '数据组'
        data_attr_col = '数据属性'

        if data_group_col not in df.columns or data_attr_col not in df.columns:
            return df, issues

        # 创建DataFrame副本用于修复
        fixed_df = df.copy()

        # 检查需要翻译的内容
        english_items = []

        for idx, row in fixed_df.iterrows():
            data_group = str(row[data_group_col]).strip()
            data_attr = str(row[data_attr_col]).strip()

            # 检查数据组是否包含纯英文
            if data_group and data_group != 'nan' and self._is_pure_english(data_group):
                english_items.append({
                    'row': idx,
                    '数据组': data_group,
                    '数据属性': data_attr
                })
            # 检查数据属性是否包含纯英文
            elif data_attr and data_attr != 'nan' and self._is_pure_english(data_attr):
                english_items.append({
                    'row': idx,
                    '数据组': data_group,
                    '数据属性': data_attr
                })

        if english_items and check_fix:
            print(f"发现 {len(english_items)} 个需要翻译的英文内容")
            
            # 批量翻译
            translated_items = self._translate_english_batch(english_items)

            # 应用翻译结果
            for i, item in enumerate(translated_items):
                if i < len(english_items):
                    original_item = english_items[i]
                    row_idx = original_item['row']

                    # 获取翻译后的数据组和数据属性
                    translated_data_group = item.get('数据组', original_item['数据组'])
                    translated_data_attr = item.get('数据属性', original_item['数据属性'])

                    # 更新DataFrame
                    if translated_data_group != original_item['数据组']:
                        fixed_df.loc[row_idx, data_group_col] = translated_data_group
                        issues.append({
                            "issue_type": "英文翻译修复",
                            "description": f"数据组 '{original_item['数据组']}' 已翻译为 '{translated_data_group}'",
                            "affected_rows": [row_idx],
                            "severity": "中",
                            "original_text": original_item['数据组'],
                            "translated_text": translated_data_group,
                            "content_type": "数据组"
                        })

                    if translated_data_attr != original_item['数据属性']:
                        fixed_df.loc[row_idx, data_attr_col] = translated_data_attr
                        issues.append({
                            "issue_type": "英文翻译修复",
                            "description": f"数据属性 '{original_item['数据属性']}' 已翻译为 '{translated_data_attr}'",
                            "affected_rows": [row_idx],
                            "severity": "中",
                            "original_text": original_item['数据属性'],
                            "translated_text": translated_data_attr,
                            "content_type": "数据属性"
                        })
        else:
            for item in english_items:
                issues.append({
                            "issue_type": "英文翻译检查",
                            "original_text": item,
                })

        print(f"英文内容检查和翻译完成，处理了 {len(issues)} 个项目")
        return fixed_df, issues

    def _is_pure_english(self, text: str) -> bool:
        """检查文本是否为纯英文（不包含中文字符）"""
        # 检查是否包含中文字符
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        has_chinese = bool(chinese_pattern.search(text))

        # 检查是否包含英文字母
        english_pattern = re.compile(r'[a-zA-Z]')
        has_english = bool(english_pattern.search(text))
        # 检查英文是否都是"ID"这个词
        if has_english and not has_chinese:
            # 提取所有英文字母
            english_chars = re.findall(r'[a-zA-Z]+', text)
            # 合并所有英文单词并转为大写
            english_text = ''.join(english_chars).upper()
            # 如果英文部分不完全是"ID"，则认为是纯英文需要翻译
            if english_text != 'ID':
                return True
        

        return False

    def _translate_english_batch(self, english_items: List[Dict]) -> List[Dict]:
        """批量翻译英文内容"""
        if not english_items:
            return english_items

        # 构建翻译提示词
        translate_prompt = """你是一个专业的中英文翻译专家，将以下json结构中的数据组和数据属性的英文翻译为中文。

请按照输入的顺序以JSON格式返回翻译结果：
请按照输入的顺序以JSON格式返回翻译结果：
```json
[
    {"数据组": "{原文或翻译结果}", "数据属性": "{原文或翻译结果}"},
    {"数据组": "{原文或翻译结果}", "数据属性": "{原文或翻译结果}"}
]
```
# 示例
输入：
```json
[
   {"row", 1, "数据组": "USER_CERT", "数据属性": "ID、USER_ID、CERT、SERIAL、CERT_NAME"},
   {"row", 2, "数据组": "DOCKER_IMAGE", "数据属性": "ID、状态、更新时间"}
]```
输入：
```json
[
   {"row", 1, "数据组": "用户证书", "数据属性": "用户证书ID、用户ID、用户证书、序列号、证书名称"},
   {"row", 2, "数据组": "容器镜像", "数据属性": "镜像ID、状态、更新时间"}
]```
"""

        user_input = f"需要翻译的内容：\n{json.dumps(english_items, ensure_ascii=False, indent=2)}"

        try:
            # 调用LLM进行翻译
            result = llm_util.call_LLM(translate_prompt, user_input + " /nothink")

            if result:
                # 解析JSON结果
                parsed_result = llm_util.extract_json_from_content(result)
                if parsed_result and isinstance(parsed_result, list):
                    # 直接返回翻译结果，格式应该是 [{"数据组": "...", "数据属性": "..."}, ...]
                    return parsed_result
                else:
                    print("翻译结果JSON解析失败")
            else:
                print("LLM翻译调用失败")

        except Exception as e:
            print(f"批量翻译失败: {e}")

        # 如果翻译失败，返回原始数据
        return english_items

    def validate_file(self, input_file: str, output_file: str = None, check_item:List[str] = ["movement","english","duplicate","long","llm"], check_fix = True) -> Dict:
        """分离式校验整个文件"""
        print(f"开始分离式校验文件: {input_file}")
        start_time = time.time()

        # 解析文件
        df = self.parse_excel_file(input_file)
        if df is None:
            return {"error": "文件解析失败"}

        print(f"原始数据: {len(df)} 行")

        fixed_df = df.copy()
        # ==================== 1. 检查修复数据移动类型的问题 ====================
        movement_issues = []
        if "movement" in check_item:
            fixed_df, movement_issues = self.check_and_fix_data_movement_sequence(fixed_df,check_fix)

        # ====================  2. 英文内容检查和翻译 ====================
        translation_issues = []
        if "english" in check_item:
            for i in range(3):
                fixed_df, issues = self.fix_english_content(fixed_df,check_fix)
                if not issues:
                    break
                translation_issues.extend(issues)
                if not check_fix:
                    break

        # ==================== 3. 数据实体名称检查和修复 ====================
        fixed_df = fixed_df
        llm_issues = []
        if "llm" in check_item and check_fix:
            fixed_df, llm_issues = self.run_llm_checks_and_fixes(fixed_df)
            
        # ====================  4. 数据重复的检查 ====================
        duplicate_issues = []
        if "duplicate" in check_item:
            # 最多重复3次执行，直到没有重复数据
            max_iterations = 1
            fixed_df, duplicate_issues = self.fix_duplicate_data_issues(fixed_df, max_iterations, check_fix)
        
        
        # ====================  5. 长功能过程的检查修复 ====================
        long_issues = []
        if "long" in check_item:
            fixed_df, long_issues = self.fix_long_function_processes(fixed_df, check_fix)

        # ====================  6. 只检查的输出 ====================
        #func_duplicate_issues = self.check_duplicate_function_processes(fixed_df)
        data_attributes_issues = self.check_duplicate_data_attributes(fixed_df)
        
        # ==================== E. 合并结果并输出 ====================
        processing_time = time.time() - start_time

        # 汇总所有结果
        all_post_llm_issues = movement_issues + translation_issues + llm_issues + duplicate_issues + long_issues
        final_result = {
            "input_file": input_file,
            "processing_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": processing_time,
            "original_records": len(df),
            "fixed_records": len(fixed_df),
            "movement_issues": movement_issues,
            "duplicate_issues": duplicate_issues,
            "translation_issues": translation_issues,
            "data_attributes_issues": data_attributes_issues,
            "llm_issues": llm_issues,
            "long_issues": long_issues
        }

        # 保存结果
        report_file, result_file = self._save_results(final_result, fixed_df, check_fix, output_file)
        if report_file:
            # 输出文件内容到控制台
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    print("=" * 50)
                    print(f.read())
            except Exception as e:
                print(f"读取报告文件失败: {e}")
            

        return final_result
    def fix_duplicate_data_issues(self, df: pd.DataFrame, max_iterations, check_fix = True) -> Tuple[pd.DataFrame, List[Dict]]:
        """修复数据重复问题，最多重复3次执行"""
        all_issues = []
        fixed_df = df.copy()
        
        for iteration in range(max_iterations):
            print(f"第 {iteration + 1} 次修复数据重复问题...")
            
            # 检查重复问题
            duplicate_issues = self.check_duplicate_data_processes(fixed_df)
            
            if not duplicate_issues:
                print("没有发现数据重复问题，修复完成")
                break
            if not check_fix:
                return fixed_df, duplicate_issues           
            
            
            # 按重复类型分组处理
            duplicate_groups = self._group_duplicate_issues(duplicate_issues, fixed_df)
            print(f"发现 {len(duplicate_issues)} 个重复问题，分为{len(duplicate_groups)} 组开始修复...")
            thread_count = self.get_optimal_thread_count(len(duplicate_groups))
            
            all_issues = []
            # 调用大模型修复每组重复问题
            # batch_group = []
            # batch_group_key = []
            # for group_key, group_data in duplicate_groups.items():
            #     batch_group.extend(group_data)
            #     batch_group_key.append(group_key)
            #     if len(batch_group) >= 10:
            #         fixed_df, group_issues = self._fix_duplicate_group(fixed_df, batch_group, batch_group_key)
            #         all_issues.extend(group_issues)
            #         batch_group = []
            #         batch_group_key = []
            
            # # 最后一组数据
            # if len(batch_group) >= 0:
            #     fixed_df, group_issues = self._fix_duplicate_group(fixed_df, batch_group, batch_group_key)
            #     all_issues.extend(group_issues)
            with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CosmicDuplicate") as executor:
                # 提交任务
                future_to_module = {}
                for group_key, group_data in duplicate_groups.items():
                    future = executor.submit(self._fix_duplicate_group, fixed_df, group_data, group_key)
                    future_to_module[future] = group_key

                # 收集结果
                for future in as_completed(future_to_module):
                    group_key = future_to_module[future]
                    try:
                        fixed_df, group_issues = future.result()
                        all_issues.extend(group_issues)
                    except Exception as exc:
                        print(f"数据组 {group_key} LLM处理失败: {exc}")
            
            # 检查是否还有重复问题
            remaining_issues = self.check_duplicate_data_processes(fixed_df)
            if not remaining_issues:
                print(f"第 {iteration + 1} 次修复后没有重复问题")
                break
            else:
                print(f"第 {iteration + 1} 次修复后仍有 {len(remaining_issues)} 个重复问题")
        
        return fixed_df, all_issues

    def _group_duplicate_issues(self, duplicate_issues: List[Dict], df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """将重复问题按相关行传递式合并分组（并查集/连通分量）"""
        # 1. 构建并查集
        parent = {}
        def find(x):
            parent.setdefault(x, x)
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        def union(x, y):
            parent[find(x)] = find(y)

        # 2. 合并所有有交集的affected_rows
        for issue in duplicate_issues:
            rows = issue['affected_rows']
            if not rows:
                continue
            first = rows[0]
            for r in rows[1:]:
                union(first, r)

        # 3. 将所有affected_rows归类到同一根节点
        group_map = {}
        for issue in duplicate_issues:
            for r in issue['affected_rows']:
                root = find(r)
                group_map.setdefault(root, set()).add(r)

        # 4. 组装分组（每组所有行的合集，组内所有issue）
        group_issues = {}
        for root, row_set in group_map.items():
            group_key = ','.join(map(str, sorted(row_set)))
            group_issues[group_key] = []
        # 5. 将issue分配到对应组
        for issue in duplicate_issues:
            # 找到该issue属于哪个组（以第一个affected_row为代表）
            if issue['affected_rows']:
                root = find(issue['affected_rows'][0])
                group_key = ','.join(map(str, sorted(group_map[root])))
                group_issues[group_key].append(issue)
        return group_issues

    def _fix_duplicate_group(self, fixed_df: pd.DataFrame, group_issues: List[Dict], group_key: str) -> Tuple[pd.DataFrame, List[Dict]]:
        """修复一组重复问题"""
        print(f"[线程{threading.current_thread().name}] 开始修复: {group_key}")
        issues = []
        #fixed_df = df.copy()
        
        try:
            # 获取所有受影响的行索引
            all_affected_rows = set()
            for issue in group_issues:
                all_affected_rows.update(issue['affected_rows'])
            
            affected_rows = sorted(list(all_affected_rows))
            
            # 获取这些行的数据
            group_data = fixed_df.loc[affected_rows].copy()
            
            # 准备输入数据
            input_data = []
            for idx, row in group_data.iterrows():
                input_data.append({
                    'row_index': idx,
                    'level1': row.get('一级功能模块', ''),
                    'level2': row.get('二级功能模块', ''),
                    'level3': row.get('三级功能模块', ''),
                    'function_process': row.get('功能过程', ''),
                    'subprocess': row.get('子过程描述', ''),
                    'data_group': row.get('数据组', ''),
                    'data_attribute': row.get('数据属性', ''),
                    'data_movement': row.get('数据移动类型', '')
                })
            
            # 分析重复类型
            duplicate_types = [issue.get('duplicate_type', '') for issue in group_issues]
            duplicate_types_str = ', '.join(set(duplicate_types))
            
            # 调用大模型
            prompt = f"""
请修复以下COSMIC功能拆解数据中的重复问题。

重复类型：{duplicate_types_str}
重复问题描述：
{chr(10).join([f"- {issue['description']}" for issue in group_issues])}

输入数据：
{json.dumps(input_data, ensure_ascii=False, indent=2)}

请返回JSON格式的结果，格式如下：
{{
    "fixed_results": [
        {{
            "row_index": 行索引,
            "subprocess": "优化后的子过程描述",
            "data_group": "优化后的数据组",
            "data_attribute": "优化后的数据属性",
            "optimization_reason": "优化理由"
        }}
    ]
}}

要求：
1. 通过补充三级功能模块或者功能过程中的业务实体名称，确保子过程描述、数据组、数据属性都不会存在重复
2. 优化后的名称应该更清晰、更具体，不要用"（","_"等连接符来区分一致性。
3. 保持原有的业务逻辑不变
4. 确保每个行索引都有对应的优化结果
"""
            
            response = llm_util.call_LLM(prompt, user_input=" ", temperature=0.2)
            result = llm_util.extract_json_from_content(response)
            
            if 'fixed_results' in result:
                # 更新数据
                for item in result['fixed_results']:
                    row_index = item.get('row_index')
                    if row_index is not None and row_index in affected_rows:
                        fixed_df.loc[row_index, '子过程描述'] = item.get('subprocess', fixed_df.loc[row_index, '子过程描述'])
                        fixed_df.loc[row_index, '数据组'] = item.get('data_group', fixed_df.loc[row_index, '数据组'])
                        fixed_df.loc[row_index, '数据属性'] = item.get('data_attribute', fixed_df.loc[row_index, '数据属性'])                        
                
                issues.append({
                    "issue_type": "数据重复修复",
                    "description": f"修复了 {len(affected_rows)} 行数据的重复问题",
                    "affected_rows": affected_rows,
                    "severity": "中",
                    "duplicate_types": duplicate_types_str,
                    "optimization_count": len(result['fixed_results'])
                })
                
        except Exception as e:
            print(f"修复重复数据时出错: {e}")
        
        return fixed_df, issues
    
    def _save_results(self, result: Dict, fixed_df: pd.DataFrame, check_fix=True, output_file: str = None) -> Tuple[str,str]:
        """保存分离式校验结果"""

        if check_fix:
            # 保存修复后的数据到Excel/CSV
            if output_file is None:
                timestamp = time.strftime("%m%d%H%M")
                output_file = os.path.join(self.output_dir, f"cosmic_validated_{timestamp}.xlsx")

            try:
                if len(fixed_df) > 0:
                    if output_file.endswith('.csv'):
                        fixed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    else:
                        writie_to_excel(fixed_df, output_file)

                    print(f"修复后的数据已保存到: {output_file}")
                else:
                    print("没有修复数据需要保存")
            except Exception as e:
                print(f"保存修复数据失败: {e}")
        
        # 生成总体报告
        report_file = os.path.join(self.output_dir, "cosmic_validation_report.txt")
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("COSMIC功能列表校验报告\n")
                f.write("=" * 50 + "\n\n")

                # 基本信息
                f.write(f"输入文件: {result.get('input_file', 'N/A')}\n")
                f.write(f"处理时间: {result.get('processing_time', 'N/A')}\n")
                f.write(f"处理耗时: {result.get('duration_seconds', 0):.2f} 秒\n\n")

                # 汇总信息
                f.write("**汇总信息**:\n")
                f.write(f"  原始记录数: {result.get('original_records', 0)}\n")
                f.write(f"  修复记录数: {result.get('fixed_records', 0)}\n")
                f.write(f"**检查修复**\n")
                f.write(f"  数据移动类型问题: {len(result.get('movement_issues', []))}\n")
                f.write(f"  英文翻译问题: {len(result.get('translation_issues', []))}\n")
                f.write(f"  数据实体名称问题: {len(result.get('llm_issues', []))}\n")
                f.write(f"  数据重复问题: {len(result.get('duplicate_issues', []))}\n")
                f.write(f"  功能过程过长问题: {len(result.get('long_issues', []))}\n")

            print(f"校验报告已保存到: {report_file}")
        except Exception as e:
            print(f"生成校验报告失败: {e}")
            report_file = None
        
        # 保存完整结果
        result_file = os.path.join(self.output_dir, config.get_CHECK_RESULT_FILE())
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"校验结果已保存到: {result_file}")
        except Exception as e:
            print(f"保存校验结果失败: {e}")
            result_file = None
        
        return report_file, result_file

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = "data/outputs/附件4：甘肃移动-模块22.xlsx"

    # 输出文件处理
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    if not output_file:
        # 生成默认输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = base_name + f"_修复_"+time.strftime("%m%d%H%M", time.localtime())+".xlsx"

    check_fix = True
    fix_items = []
    if len(sys.argv) > 3:
        fix_items = sys.argv[3].split(",")
    else:
        all_items = ["movement","english","llm","duplicate","long"]
        # 输出可修复列表，让用户选择
        print("可修复的项目：")
        print("0. 修复全部问题")
        print("1. 数据移动类型问题")
        print("2. 英文名称问题")
        print("3. 数据名称缺失实体的问题")
        print("4. 数据重复问题")
        print("5. 功能过程过长")
        print("6. 仅检查不修复")
        # 等待用户选择
        choice = input("请输入要修复的问题编号：")
        if choice == '0':
            fix_items = all_items
        elif choice == '6':
            fix_items = all_items
            check_fix = False
        else:
            fix_no = int(choice)
            if fix_no > 5:
                print("无效的选择")
                exit()
            fix_items = all_items[fix_no-1:fix_no]
    
    print("COSMIC功能拆解校验器")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("=" * 50)

    # 创建校验器
    validator = CosmicValidator()

    # 执行校验
    result = validator.validate_file(input_file, output_file,fix_items,check_fix)

    if "error" in result:
        print(f"校验失败: {result['error']}")
    else:
        print("\n" + "=" * 50)
        print("Cosmic校验完成！")
        print("=" * 50)

        summary = result.get('summary', {})
        print(f"总问题数: {summary.get('total_issues', 0)}")
        print(f"\n详细报告请查看: {validator.output_dir}/cosmic_validation_report.txt")


if __name__ == "__main__":
    main()
