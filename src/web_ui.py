#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能拆解系统 Web UI

提供Web界面用于：
1. 软件功能清单文件上传管理
2. COSMIC功能拆解
3. 拆解结果检查和修复
4. Word文档转换
5. 配置管理
"""

import os
import glob
import json
import time
import shutil
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS
from werkzeug.utils import secure_filename
import pandas as pd
import markdown
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import html
import llm_util

# 导入项目模块
from main import do_cosmic_file
from validator import CosmicValidator
from doc_generator import RequirementGenerator
try:
    from step6_generate_mermaid import replace_mermaid_with_images
except ImportError:
    # 如果导入失败，创建一个占位函数
    def replace_mermaid_with_images(md_file):
        return None
import config

app = Flask(__name__, template_folder='../templates', static_folder='../static')
CORS(app)

# 配置
UPLOAD_FOLDER = 'data/uploads'
OUTPUT_FOLDER = 'data/outputs'
WORDS_FOLDER = 'data/words'
PROMPTS_FOLDER = 'data/prompts'
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER
app.config['WORDS_FOLDER'] = WORDS_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# 确保目录存在
for folder in [UPLOAD_FOLDER, OUTPUT_FOLDER, WORDS_FOLDER, PROMPTS_FOLDER]:
    os.makedirs(folder, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_excel_format(file_path):
    """验证Excel文件格式"""
    try:
        # 读取第2个sheet（索引为1）
        df = llm_util.parse_excel_file(file_path)
        
        # 检查必需的列
        required_columns = [
            '一级功能模块', '二级功能模块', '三级功能模块', 
            '功能过程', '功能描述', '预估工作量（人天）'
        ]
        
        missing_columns = []
        for col in required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            return False, f"缺少必需的列: {', '.join(missing_columns)}"
        
        return True, "格式验证通过"
    
    except Exception as e:
        return False, f"文件读取错误: {str(e)}"

def get_uploaded_files():
    """获取已上传的文件列表"""
    files = []
    if os.path.exists(UPLOAD_FOLDER):
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                file_info = {
                    'name': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': os.path.getmtime(file_path),
                    'modified_str': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                }
                files.append(file_info)
    
    # 按修改时间排序
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def get_output_files():
    """获取输出文件列表"""
    files = []
    if os.path.exists(OUTPUT_FOLDER):
        for filename in os.listdir(OUTPUT_FOLDER):
            if filename.endswith(('.xlsx', '.csv')):
                file_path = os.path.join(OUTPUT_FOLDER, filename)
                file_info = {
                    'name': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': os.path.getmtime(file_path),
                    'modified_str': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                }
                files.append(file_info)
    
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def get_word_files():
    """获取Word文档文件列表"""
    files = []
    if os.path.exists(WORDS_FOLDER):
        for filename in os.listdir(WORDS_FOLDER):
            if filename.endswith(('.md', '.docx')):
                file_path = os.path.join(WORDS_FOLDER, filename)
                file_info = {
                    'name': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': os.path.getmtime(file_path),
                    'modified_str': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                }
                files.append(file_info)
    
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def get_prompt_files():
    """获取提示词文件列表"""
    files = []
    prompt_dir = 'prompt'
    if os.path.exists(prompt_dir):
        for filename in os.listdir(prompt_dir):
            # 显示以'prompt'开头的.md文件和系统修复提示词
            if filename.endswith('.md') and (filename.lower().startswith('prompt') or filename == 'check_fix_prompt.md'):
                file_path = os.path.join(prompt_dir, filename)

                # 特殊处理系统修复提示词
                if filename == 'check_fix_prompt.md':
                    display_name = '系统修复提示词'
                    is_system = True
                else:
                    display_name = filename.replace('.md', '').replace('prompt_', '').replace('prompt', '提示词')
                    is_system = False

                file_info = {
                    'name': filename,
                    'path': file_path,
                    'display_name': display_name,
                    'is_system': is_system
                }
                files.append(file_info)

    # 按文件名排序，系统文件排在前面
    files.sort(key=lambda x: (not x.get('is_system', False), x['name']))
    return files
def generate_flowchart_html(sequence_content):
    """从时序图内容生成流程图HTML"""
    import re

    # 解析时序图内容，提取参与者和交互
    participants = []
    interactions = []
    participant_map = {}

    lines = sequence_content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('participant '):
            # 提取参与者
            match = re.match(r'participant\s+(\w+)\s+as\s+(.+)', line)
            if match:
                participant_id = match.group(1)
                participant_name = match.group(2)
                participants.append({'id': participant_id, 'name': participant_name})
                participant_map[participant_id] = participant_name
        elif any(arrow in line for arrow in ['->', '-->>', '-->']):
            # 提取交互
            arrow_type = 'solid'
            if '->>' in line:
                parts = line.split('->>')
                arrow_type = 'solid'
            elif '-->' in line:
                parts = line.split('-->')
                arrow_type = 'dashed'
            elif '->' in line:
                parts = line.split('->')
                arrow_type = 'solid'
            else:
                continue

            if len(parts) == 2:
                from_actor = parts[0].strip()
                to_and_msg = parts[1].strip()
                if ':' in to_and_msg:
                    to_actor, message = to_and_msg.split(':', 1)
                    to_actor = to_actor.strip()
                    message = message.strip()
                else:
                    to_actor = to_and_msg
                    message = ''

                # 查找参与者在列表中的位置
                from_index = -1
                to_index = -1
                for i, p in enumerate(participants):
                    if p['id'] == from_actor:
                        from_index = i
                    if p['id'] == to_actor:
                        to_index = i

                if from_index >= 0 and to_index >= 0:
                    interactions.append({
                        'from': from_actor,
                        'to': to_actor,
                        'from_index': from_index,
                        'to_index': to_index,
                        'message': message,
                        'type': arrow_type
                    })

    if not participants:
        return '<div class="flowchart-container"><p>无法解析时序图内容</p></div>'

    # 生成流程图HTML
    flowchart_html = '<div class="flowchart-container">'

    # 添加参与者框
    for i, participant in enumerate(participants):
        flowchart_html += f'''
        <div class="participant-box" data-id="{participant['id']}" style="left: {i * 180 + 50}px;">
            {participant['name']}
        </div>
        '''

    # 添加交互箭头和消息
    for i, interaction in enumerate(interactions):
        arrow_class = 'solid-arrow' if interaction['type'] == 'solid' else 'dashed-arrow'

        # 计算箭头位置
        from_x = interaction['from_index'] * 180 + 110  # 参与者框中心
        to_x = interaction['to_index'] * 180 + 110
        y_pos = 120 + i * 60

        # 确定箭头方向
        if from_x < to_x:
            arrow_left = from_x
            arrow_width = to_x - from_x
            arrow_direction = 'right'
        else:
            arrow_left = to_x
            arrow_width = from_x - to_x
            arrow_direction = 'left'

        flowchart_html += f'''
        <div class="interaction" style="top: {y_pos}px; left: {arrow_left}px; width: {arrow_width}px;">
            <div class="arrow {arrow_class} arrow-{arrow_direction}"></div>
            <div class="message">{interaction['message']}</div>
        </div>
        '''

    flowchart_html += '</div>'
    return flowchart_html
def render_markdown_with_mermaid(markdown_content):
    """渲染Markdown内容，支持Mermaid图表和流程图备选方案"""
    # 使用markdown库渲染基本内容
    md = markdown.Markdown(extensions=['codehilite', 'fenced_code', 'tables', 'toc'])
    html_content = md.convert(markdown_content)

    # 查找并处理时序图，添加流程图备选方案
    import re

    def replace_mermaid_with_fallback(match):
        mermaid_content = match.group(1)
        if 'sequenceDiagram' in mermaid_content:
            # 生成流程图备选方案
            flowchart_html = generate_flowchart_html(mermaid_content)
            return f'''
            <div class="diagram-container">
                <div class="mermaid-diagram">
                    <pre class="mermaid">{mermaid_content}</pre>
                </div>
                <div class="fallback-flowchart" style="display: none;">
                    {flowchart_html}
                </div>
                <div class="diagram-toggle">
                    <button onclick="toggleDiagram(this)" class="toggle-btn">切换到流程图</button>
                </div>
            </div>
            '''
        else:
            return f'<pre class="mermaid">{mermaid_content}</pre>'

    # 替换mermaid代码块
    html_content = re.sub(r'<pre><code class="language-mermaid">(.*?)</code></pre>',
                         replace_mermaid_with_fallback, html_content, flags=re.DOTALL)

    # 读取HTML模板文件
    template_path = os.path.join('templates', 'document_template.html')
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            html_template = f.read()

        # 替换模板中的占位符
        html_template = html_template.replace('{{html_content}}', html_content)

    except FileNotFoundError:
        # 如果模板文件不存在，使用简单的HTML包装
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>功能需求文档</title>
            <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
        </head>
        <body>
            <div class="container">
                {html_content}
            </div>
            <script>
                mermaid.initialize({{
                    startOnLoad: true,
                    theme: 'default',
                    securityLevel: 'loose'
                }});
            </script>
        </body>
        </html>
        """

    return html_template
def generate_excel_preview_html(file_path, df):
    """生成Excel预览HTML，支持合并单元格样式"""
    html_table = df.to_html(classes='table table-striped table-bordered table-sm',
                               table_id='preview-table',
                               escape=False)
    return f'<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">{html_table}</div>'
    try:
        import openpyxl
        from openpyxl import load_workbook

        # 加载工作簿以获取合并单元格信息
        wb = load_workbook(file_path)
        ws = wb.active

        # 获取合并单元格范围
        merged_ranges = list(ws.merged_cells.ranges)

        # 生成HTML表格
        html = '<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">'
        html += '<table class="table table-striped table-bordered table-sm" id="preview-table">'

        # 表头
        html += '<thead class="table-dark sticky-top">'
        html += '<tr>'
        for col in df.columns:
            html += f'<th>{col}</th>'
        html += '</tr>'
        html += '</thead>'

        # 表体
        html += '<tbody>'
        for idx, row in df.iterrows():
            html += '<tr>'
            for col_idx, (col, value) in enumerate(row.items()):
                # 检查是否在合并单元格中
                cell_coord = f"{openpyxl.utils.get_column_letter(col_idx + 1)}{idx + 2}"  # +2因为有表头
                is_merged = False
                colspan = 1
                rowspan = 1

                for merged_range in merged_ranges:
                    if cell_coord in merged_range:
                        is_merged = True
                        # 计算合并范围
                        min_col, min_row, max_col, max_row = merged_range.bounds
                        if cell_coord == f"{openpyxl.utils.get_column_letter(min_col)}{min_row}":
                            colspan = max_col - min_col + 1
                            rowspan = max_row - min_row + 1
                        else:
                            # 如果不是合并单元格的左上角，跳过
                            continue
                        break

                # 处理空值显示
                display_value = str(value) if pd.notna(value) else ''

                # 生成单元格HTML
                cell_attrs = []
                if colspan > 1:
                    cell_attrs.append(f'colspan="{colspan}"')
                if rowspan > 1:
                    cell_attrs.append(f'rowspan="{rowspan}"')

                attrs_str = ' ' + ' '.join(cell_attrs) if cell_attrs else ''
                html += f'<td{attrs_str}>{display_value}</td>'

            html += '</tr>'

        html += '</tbody>'
        html += '</table>'
        html += '</div>'

        # 添加统计信息
        html += f'<div class="mt-3 text-muted">'
        html += f'<small>共 {len(df)} 行 × {len(df.columns)} 列</small>'
        html += '</div>'

        return html

    except ImportError:
        # 如果没有openpyxl，使用简单的表格
        html_table = df.to_html(classes='table table-striped table-bordered table-sm',
                               table_id='preview-table',
                               escape=False)
        return f'<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">{html_table}</div>'
    except Exception as e:
        # 出错时使用简单的表格
        html_table = df.to_html(classes='table table-striped table-bordered table-sm',
                               table_id='preview-table',
                               escape=False)
        return f'<div class="table-responsive" style="max-height: 600px; overflow-y: auto;">{html_table}</div>'

@app.route('/')
def index():
    """主页面"""
    uploaded_files = get_uploaded_files()
    output_files = get_output_files()
    word_files = get_word_files()
    prompt_files = get_prompt_files()

    # 检查是否有修复文件
    has_repair_files = any('_修复_' in file['name'] for file in output_files)

    return render_template('index.html',
                         uploaded_files=uploaded_files,
                         output_files=output_files,
                         word_files=word_files,
                         prompt_files=prompt_files,
                         has_repair_files=has_repair_files)

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """文件上传API"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file and allowed_file(file.filename):
            #filename = secure_filename(file.filename)
            filename = file.filename
            # 如果文件已存在，添加时间戳
            if os.path.exists(os.path.join(UPLOAD_FOLDER, filename)):
                name, ext = os.path.splitext(filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{name}_{timestamp}{ext}"
            
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(file_path)
            
            # 验证文件格式
            is_valid, message = validate_excel_format(file_path)
            if not is_valid:
                # 删除文件
                os.remove(file_path)
            
            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'filename': filename,
                'validation': {
                    'valid': is_valid,
                    'message': message
                }
            })
        else:
            return jsonify({'error': '不支持的文件格式'}), 400
            
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/delete_upload/<filename>', methods=['DELETE'])
def delete_upload_file(filename):
    """删除上传的文件"""
    try:
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '文件删除成功'})
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/api/cosmic_decompose', methods=['POST'])
def cosmic_decompose():
    """COSMIC功能拆解API"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        prompt_file = data.get('prompt_file', 'prompt/prompt.md')
        
        if not filename:
            return jsonify({'error': '未指定文件'}), 400
        
        input_file = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404
        
        # 生成输出文件名
        base_name = os.path.splitext(filename)[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"{base_name}_拆解_{timestamp}.xlsx"
        output_file = os.path.join(OUTPUT_FOLDER, output_filename)
        
        # 调用拆解功能
        result = do_cosmic_file(input_file, output_file, prompt_file)
        
        if os.path.exists(output_file):
            return jsonify({
                'success': True,
                'message': 'COSMIC功能拆解完成',
                'output_filename': output_filename
            })
        else:
            return jsonify({'error': 'COSMIC功能拆解失败'}), 500
            
    except Exception as e:
        return jsonify({'error': f'拆解失败: {str(e)}'}), 500

@app.route('/api/validate_repair', methods=['POST'])
def validate_repair():
    """检查和修复COSMIC拆解文件"""
    try:
        data = request.get_json()
        filename = data.get('filename')

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        input_file = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404

        # 生成修复文件名
        base_name = os.path.splitext(filename)[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        repair_filename = f"{base_name}_修复_{timestamp}.xlsx"
        repair_file = os.path.join(OUTPUT_FOLDER, repair_filename)

        # 调用校验修复功能
        validator = CosmicValidator()
        result = validator.validate_file(input_file, repair_file)

        if os.path.exists(repair_file):
            return jsonify({
                'success': True,
                'message': '检查修复完成',
                'repair_filename': repair_filename,
                'report': result
            })
        else:
            return jsonify({'error': '检查修复失败'}), 500

    except Exception as e:
        return jsonify({'error': f'检查修复失败: {str(e)}'}), 500

@app.route('/api/generate_document', methods=['POST'])
def generate_document():
    """生成Word文档"""
    try:
        data = request.get_json()
        filename = data.get('filename')

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        input_file = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(input_file):
            return jsonify({'error': '文件不存在'}), 404

        # 生成markdown文件名
        base_name = os.path.splitext(filename)[0]
        md_filename = f"{base_name}_功能需求文档.md"
        md_file = os.path.join(WORDS_FOLDER, md_filename)

        # 调用文档生成功能
        generator = RequirementGenerator()
        result = generator.generate_requirements_document(input_file, md_file)

        if os.path.exists(md_file):
            return jsonify({
                'success': True,
                'message': '文档生成完成',
                'md_filename': md_filename
            })
        else:
            return jsonify({'error': '文档生成失败'}), 500

    except Exception as e:
        return jsonify({'error': f'文档生成失败: {str(e)}'}), 500

@app.route('/api/convert_to_word', methods=['POST'])
def convert_to_word():
    """将Markdown转换为Word文档"""
    try:
        data = request.get_json()
        filename = data.get('filename')

        if not filename:
            return jsonify({'error': '未指定文件'}), 400

        md_file = os.path.join(WORDS_FOLDER, filename)
        if not os.path.exists(md_file):
            return jsonify({'error': '文件不存在'}), 404

        # 调用转换功能
        word_file = replace_mermaid_with_images(md_file)

        if word_file and os.path.exists(word_file):
            return send_file(word_file, as_attachment=True)
        else:
            return jsonify({'error': 'Word转换失败'}), 500

    except Exception as e:
        return jsonify({'error': f'转换失败: {str(e)}'}), 500

@app.route('/api/delete_output/<filename>', methods=['DELETE'])
def delete_output_file(filename):
    """删除输出文件"""
    try:
        file_path = os.path.join(OUTPUT_FOLDER, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '文件删除成功'})
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/api/delete_word/<filename>', methods=['DELETE'])
def delete_word_file(filename):
    """删除Word文档文件"""
    try:
        file_path = os.path.join(WORDS_FOLDER, filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '文件删除成功'})
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/download/<path:folder>/<filename>')
def download_file(folder, filename):
    """下载文件"""
    try:
        if folder == 'uploads':
            file_path = os.path.join(UPLOAD_FOLDER, filename)
        elif folder == 'outputs':
            file_path = os.path.join(OUTPUT_FOLDER, filename)
        elif folder == 'words':
            file_path = os.path.join(WORDS_FOLDER, filename)
        else:
            return jsonify({'error': '无效的文件夹'}), 400

        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.route('/api/preview/<path:folder>/<filename>')
def preview_file(folder, filename):
    """预览文件"""
    try:
        if folder == 'uploads':
            file_path = os.path.join(UPLOAD_FOLDER, filename)
        elif folder == 'outputs':
            file_path = os.path.join(OUTPUT_FOLDER, filename)
        elif folder == 'words':
            file_path = os.path.join(WORDS_FOLDER, filename)
        else:
            return jsonify({'error': '无效的文件夹'}), 400

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 根据文件类型处理预览
        if filename.endswith('.md'):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            html_content = render_markdown_with_mermaid(content)
            return html_content
        elif filename.endswith(('.xlsx', '.xls')):
            # Excel文件预览 - 显示所有行并支持合并单元格
            df = llm_util.parse_excel_file(file_path)

            # 生成带样式的HTML表格
            html_table = generate_excel_preview_html(file_path, df)
            return render_template('preview.html', content=html_table, filename=filename)
        else:
            return jsonify({'error': '不支持的文件类型预览'}), 400

    except Exception as e:
        return jsonify({'error': f'预览失败: {str(e)}'}), 500

@app.route('/api/preview_markdown', methods=['POST'])
def preview_markdown():
    """预览Markdown内容，支持Mermaid渲染"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content.strip():
            return '<html><body><p>内容为空</p></body></html>'

        # 使用markdown渲染
        html_content = markdown.markdown(content, extensions=['tables', 'fenced_code'])

        # 使用document_template.html渲染
        return render_template('document_template.html', html_content=html_content)

    except Exception as e:
        return f'<html><body><h1>预览失败</h1><p>{str(e)}</p></body></html>'

@app.route('/api/prompt/<path:filename>')
def get_prompt_content(filename):
    """获取提示词内容"""
    try:
        if not os.path.exists(filename):
            return jsonify({'error': '文件不存在'}), 404

        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        return jsonify({
            'success': True,
            'content': content,
            'filename': filename
        })

    except Exception as e:
        return jsonify({'error': f'读取失败: {str(e)}'}), 500

@app.route('/api/prompt/<path:filename>', methods=['POST'])
def save_prompt_content(filename):
    """保存提示词内容"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content.strip():
            return jsonify({'error': '内容不能为空'}), 400

        # 备份原文件
        if os.path.exists(filename):
            backup_filename = f"{filename}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(filename, backup_filename)

        # 保存新内容
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        return jsonify({
            'success': True,
            'message': '提示词保存成功',
            'filename': filename
        })

    except Exception as e:
        return jsonify({'error': f'保存失败: {str(e)}'}), 500

@app.route('/api/prompt/save-as', methods=['POST'])
def save_prompt_as():
    """另存提示词内容为新文件"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        new_filename = data.get('filename', '')

        if not content.strip():
            return jsonify({'error': '内容不能为空'}), 400

        if not new_filename.strip():
            return jsonify({'error': '文件名不能为空'}), 400

        # 确保文件名以prompt开头并以.md结尾
        if not new_filename.startswith('prompt'):
            new_filename = f"prompt_{new_filename}"

        if not new_filename.endswith('.md'):
            new_filename = f"{new_filename}.md"

        # 构建完整路径
        full_path = os.path.join('prompt', new_filename)

        # 检查文件是否已存在
        if os.path.exists(full_path):
            return jsonify({'error': '文件名已存在，请选择其他名称'}), 400

        # 确保prompt目录存在
        os.makedirs('prompt', exist_ok=True)

        # 保存新文件
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return jsonify({
            'success': True,
            'message': '提示词另存成功',
            'filename': new_filename,
            'full_path': full_path
        })

    except Exception as e:
        return jsonify({'error': f'另存失败: {str(e)}'}), 500

@app.route('/api/config')
def get_config():
    """获取配置信息"""
    try:
        env_file = 'src/.env'
        config_data = {}

        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config_data[key.strip()] = value.strip()

        return jsonify({
            'success': True,
            'config': config_data
        })

    except Exception as e:
        return jsonify({'error': f'获取配置失败: {str(e)}'}), 500

@app.route('/api/config', methods=['POST'])
def save_config():
    """保存配置信息"""
    try:
        data = request.get_json()
        config_data = data.get('config', {})

        env_file = 'src/.env'

        # 备份原配置文件
        if os.path.exists(env_file):
            backup_file = f"{env_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(env_file, backup_file)

        # 读取原配置文件，保留注释和结构
        original_lines = []
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                original_lines = f.readlines()

        # 更新配置
        new_lines = []
        updated_keys = set()

        for line in original_lines:
            stripped_line = line.strip()
            if stripped_line and not stripped_line.startswith('#') and '=' in stripped_line:
                key = stripped_line.split('=', 1)[0].strip()
                if key in config_data:
                    new_lines.append(f"{key}={config_data[key]}\n")
                    updated_keys.add(key)
                else:
                    new_lines.append(line)
            else:
                new_lines.append(line)

        # 添加新的配置项
        for key, value in config_data.items():
            if key not in updated_keys:
                new_lines.append(f"{key}={value}\n")

        # 写入新配置
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)

        return jsonify({
            'success': True,
            'message': '配置保存成功'
        })

    except Exception as e:
        return jsonify({'error': f'保存配置失败: {str(e)}'}), 500

if __name__ == '__main__':
    port=5002
    print("=== COSMIC功能拆解系统 Web UI ===")
    app.run(debug=True, host='0.0.0.0', port=port)
